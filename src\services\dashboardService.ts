import prisma from "../db/db";
import { DashboardStats } from "../types";
import { UserRole, TransactionStatus, ApprovalStage } from "@prisma/client";

export class DashboardService {
  static async getUserDashboardStats(
    userId: string,
    userRole: UserRole
  ): Promise<DashboardStats> {
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    let whereClause: any = {};

    // Account officers see only their own transactions
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      whereClause.createdById = userId;
    }

    // Get overall stats
    const [
      totalTransactions,
      pendingTransactions,
      approvedTransactions,
      rejectedTransactions,
      totalAmountResult,
      monthlyTransactions,
      monthlyAmountResult,
      userInfo,
    ] = await Promise.all([
      // Total transactions
      prisma.transaction.count({ where: whereClause }),

      // Pending transactions
      prisma.transaction.count({
        where: {
          ...whereClause,
          status: {
            in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
          },
        },
      }),

      // Approved transactions
      prisma.transaction.count({
        where: {
          ...whereClause,
          status: TransactionStatus.APPROVED,
        },
      }),

      // Rejected transactions
      prisma.transaction.count({
        where: {
          ...whereClause,
          status: TransactionStatus.REJECTED,
        },
      }),

      // Total amount
      prisma.transaction.aggregate({
        where: {
          ...whereClause,
          status: TransactionStatus.APPROVED,
        },
        _sum: {
          requestedAmount: true,
        },
      }),

      // Monthly transactions
      prisma.transaction.count({
        where: {
          ...whereClause,
          createdAt: {
            gte: currentMonth,
            lt: nextMonth,
          },
        },
      }),

      // Monthly amount - should match performance overview logic
      prisma.transaction.aggregate({
        where: {
          ...whereClause,
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT, // Final approval stage
          createdAt: {
            gte: currentMonth,
            lt: nextMonth,
          },
        },
        _sum: {
          requestedAmount: true,
        },
      }),

      // User info (for monthly target)
      userRole === UserRole.ACCOUNT_OFFICER
        ? prisma.user.findUnique({
            where: { id: userId },
            select: { monthlyTarget: true },
          })
        : null,
    ]);

    const totalAmount = totalAmountResult._sum.requestedAmount || 0;
    const monthlyAmount = monthlyAmountResult._sum.requestedAmount || 0;
    const successRate =
      totalTransactions > 0
        ? (approvedTransactions / totalTransactions) * 100
        : 0;

    const stats: DashboardStats = {
      totalTransactions,
      pendingTransactions,
      approvedTransactions,
      rejectedTransactions,
      totalAmount,
      successRate: Math.round(successRate * 100) / 100,
    };

    // Add monthly target and progress for account officers
    if (userRole === UserRole.ACCOUNT_OFFICER && userInfo?.monthlyTarget) {
      stats.monthlyTarget = userInfo.monthlyTarget;
      stats.monthlyProgress =
        Math.round((monthlyAmount / userInfo.monthlyTarget) * 100 * 100) / 100;
    }

    return stats;
  }

  static async getSystemOverview(): Promise<{
    totalUsers: number;
    activeUsers: number;
    totalTransactions: number;
    pendingApprovals: number;
    monthlyVolume: number;
    topPerformers: any[];
  }> {
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const [
      totalUsers,
      activeUsers,
      totalTransactions,
      pendingApprovals,
      monthlyVolumeResult,
      topPerformers,
    ] = await Promise.all([
      // Total users
      prisma.user.count(),

      // Active users
      prisma.user.count({
        where: { isActive: true },
      }),

      // Total transactions
      prisma.transaction.count(),

      // Pending approvals
      prisma.transaction.count({
        where: {
          status: {
            in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
          },
        },
      }),

      // Monthly volume
      prisma.transaction.aggregate({
        where: {
          status: TransactionStatus.APPROVED,
          createdAt: {
            gte: currentMonth,
            lt: nextMonth,
          },
        },
        _sum: {
          requestedAmount: true,
        },
      }),

      // Top performers (account officers with most approved transactions this month)
      prisma.user.findMany({
        where: {
          role: UserRole.ACCOUNT_OFFICER,
          isActive: true,
        },
        include: {
          createdTransactions: {
            where: {
              status: TransactionStatus.APPROVED,
              createdAt: {
                gte: currentMonth,
                lt: nextMonth,
              },
            },
            select: {
              requestedAmount: true,
            },
          },
        },
        take: 5,
      }),
    ]);

    const monthlyVolume = monthlyVolumeResult._sum.requestedAmount || 0;

    // Process top performers
    const processedTopPerformers = topPerformers
      .map((user) => ({
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        transactionCount: user.createdTransactions.length,
        totalAmount: user.createdTransactions.reduce(
          (sum, transaction) => sum + (transaction.requestedAmount || 0),
          0
        ),
        monthlyTarget: user.monthlyTarget || 0,
      }))
      .sort((a, b) => b.totalAmount - a.totalAmount);

    return {
      totalUsers,
      activeUsers,
      totalTransactions,
      pendingApprovals,
      monthlyVolume,
      topPerformers: processedTopPerformers,
    };
  }

  static async getTransactionTrends(days: number = 30): Promise<{
    daily: Array<{ date: string; count: number; amount: number }>;
    statusBreakdown: Record<string, number>;
  }> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get daily transaction data (MongoDB aggregation)
    const dailyData = await prisma.transaction.groupBy({
      by: ["createdAt"],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: {
        id: true,
      },
      _sum: {
        requestedAmount: true,
      },
    });

    // Get status breakdown
    const statusData = await prisma.transaction.groupBy({
      by: ["status"],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: {
        status: true,
      },
    });

    const statusBreakdown: Record<string, number> = {};
    statusData.forEach((item) => {
      statusBreakdown[item.status] = item._count.status;
    });

    return {
      daily: dailyData.map((item) => ({
        date: item.createdAt.toISOString().split("T")[0],
        count: item._count.id,
        amount: item._sum.requestedAmount || 0,
      })),
      statusBreakdown,
    };
  }

  static async getApprovalMetrics(): Promise<{
    averageApprovalTime: number;
    approvalsByStage: Record<string, number>;
    rejectionReasons: Array<{ reason: string; count: number }>;
  }> {
    // Get approved transactions with timing data
    const approvedTransactions = await prisma.transaction.findMany({
      where: {
        status: TransactionStatus.APPROVED,
        submittedAt: { not: null },
        completedAt: { not: null },
      },
      select: {
        submittedAt: true,
        completedAt: true,
      },
    });

    // Calculate average approval time in hours
    const approvalTimes = approvedTransactions
      .filter((t) => t.submittedAt && t.completedAt)
      .map((t) => {
        const submitted = new Date(t.submittedAt!);
        const completed = new Date(t.completedAt!);
        return (completed.getTime() - submitted.getTime()) / (1000 * 60 * 60); // Convert to hours
      });

    const averageApprovalTime =
      approvalTimes.length > 0
        ? approvalTimes.reduce((sum, time) => sum + time, 0) /
          approvalTimes.length
        : 0;

    // Get approvals by stage
    const stageData = await prisma.transactionApproval.groupBy({
      by: ["stage"],
      _count: {
        stage: true,
      },
    });

    const approvalsByStage: Record<string, number> = {};
    stageData.forEach((item) => {
      approvalsByStage[item.stage] = item._count.stage;
    });

    // Get rejection reasons
    const rejectionData = await prisma.transaction.findMany({
      where: {
        status: TransactionStatus.REJECTED,
        rejectionReason: {
          not: null,
        },
      },
      select: {
        rejectionReason: true,
      },
    });

    const reasonCounts: Record<string, number> = {};
    rejectionData.forEach((item) => {
      if (item.rejectionReason) {
        reasonCounts[item.rejectionReason] =
          (reasonCounts[item.rejectionReason] || 0) + 1;
      }
    });

    const rejectionReasons = Object.entries(reasonCounts)
      .map(([reason, count]) => ({ reason, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 reasons

    return {
      averageApprovalTime: Math.round(averageApprovalTime * 100) / 100,
      approvalsByStage,
      rejectionReasons,
    };
  }

  static async getMyTransactions(
    filters: {
      status?: string;
      dateFrom?: Date;
      dateTo?: Date;
      search?: string;
      accountOfficerName?: string;
    },
    pagination: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: "asc" | "desc";
    },
    userId: string,
    userRole: UserRole
  ): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const whereClause: any = {};

    // Role-based filtering
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      // Account officers see only their own transactions
      whereClause.createdById = userId;
    } else {
      // For all other roles, only show disbursed/completed transactions
      whereClause.status = {
        in: [TransactionStatus.DISBURSED, TransactionStatus.COMPLETED],
      };
    }

    // Apply filters
    if (filters.status) {
      whereClause.status = filters.status;
    }

    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};
      if (filters.dateFrom) {
        whereClause.createdAt.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        // Add 1 day to include the entire end date
        const endDate = new Date(filters.dateTo);
        endDate.setDate(endDate.getDate() + 1);
        whereClause.createdAt.lt = endDate;
      }
    }

    // Search functionality
    if (filters.search) {
      whereClause.OR = [
        {
          transactionId: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          firstName: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          lastName: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          email: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
      ];
    }

    // Filter by account officer name (for non-account officers)
    if (filters.accountOfficerName && userRole !== UserRole.ACCOUNT_OFFICER) {
      whereClause.createdBy = {
        OR: [
          {
            firstName: {
              contains: filters.accountOfficerName,
              mode: "insensitive",
            },
          },
          {
            lastName: {
              contains: filters.accountOfficerName,
              mode: "insensitive",
            },
          },
        ],
      };
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where: whereClause,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          documents: {
            select: {
              id: true,
              originalName: true,
              fileType: true,
            },
          },
          approvals: {
            select: {
              stage: true,
              action: true,
              createdAt: true,
              approver: {
                select: {
                  firstName: true,
                  lastName: true,
                  role: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 1, // Get latest approval
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where: whereClause }),
    ]);

    // Format transactions for frontend
    const formattedTransactions = transactions.map((transaction) => ({
      id: transaction.id,
      transactionId: transaction.transactionId,
      customerName:
        `${transaction.firstName || ""} ${transaction.lastName || ""}`.trim() ||
        "N/A",
      accountOfficer: {
        id: transaction.createdBy.id,
        name: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`,
        email: transaction.createdBy.email,
      },
      status: transaction.status,
      currentStage: transaction.currentStage,
      requestedAmount: transaction.requestedAmount,
      createdAt: transaction.createdAt,
      submittedAt: transaction.submittedAt,
      completedAt: transaction.completedAt,
      documentsCount: transaction.documents.length,
      latestApproval: transaction.approvals[0] || null,
      canEdit:
        transaction.status === "DRAFT" || transaction.status === "SENT_BACK",
      // Add status-specific information
      statusInfo: this.getStatusInfo(
        transaction.status,
        transaction.currentStage || undefined
      ),
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      transactions: formattedTransactions,
      total,
      page,
      limit,
      totalPages,
    };
  }

  static async getMyTransactionStats(
    userId: string,
    userRole: UserRole,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<{
    totalTransactions: number;
    disbursed: number;
    pending: number;
    rejected: number;
    repaid: number;
    draft: number;
    sentBack: number;
    totalAmount: number;
    disbursedAmount: number;
  }> {
    const whereClause: any = {};

    // Role-based filtering
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      whereClause.createdById = userId;
    }

    // Date filtering
    if (dateFrom || dateTo) {
      whereClause.createdAt = {};
      if (dateFrom) {
        whereClause.createdAt.gte = dateFrom;
      }
      if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setDate(endDate.getDate() + 1);
        whereClause.createdAt.lt = endDate;
      }
    }

    // Get counts by status
    const [statusCounts, totalAmountResult, disbursedAmountResult] =
      await Promise.all([
        prisma.transaction.groupBy({
          by: ["status"],
          where: whereClause,
          _count: {
            status: true,
          },
        }),
        prisma.transaction.aggregate({
          where: whereClause,
          _sum: {
            requestedAmount: true,
          },
        }),
        prisma.transaction.aggregate({
          where: {
            ...whereClause,
            status: TransactionStatus.COMPLETED,
          },
          _sum: {
            requestedAmount: true,
          },
        }),
      ]);

    // Initialize stats
    const stats = {
      totalTransactions: 0,
      disbursed: 0,
      pending: 0,
      rejected: 0,
      repaid: 0, // For future implementation
      draft: 0,
      sentBack: 0,
      totalAmount: totalAmountResult._sum.requestedAmount || 0,
      disbursedAmount: disbursedAmountResult._sum.requestedAmount || 0,
    };

    // Process status counts
    statusCounts.forEach((item) => {
      const count = item._count.status;
      stats.totalTransactions += count;

      switch (item.status) {
        case TransactionStatus.DISBURSED:
        case TransactionStatus.COMPLETED:
        case TransactionStatus.LOAN_REPAID:
          stats.disbursed += count;
          break;
        case TransactionStatus.SUBMITTED:
        case TransactionStatus.IN_PROGRESS:
        case TransactionStatus.APPROVED:
          stats.pending += count;
          break;
        case TransactionStatus.REJECTED:
          stats.rejected += count;
          break;
        case TransactionStatus.DRAFT:
          stats.draft += count;
          break;
        case TransactionStatus.SENT_BACK:
          stats.sentBack += count;
          break;
      }
    });

    return stats;
  }

  static async getAccountOfficersList(): Promise<
    Array<{
      id: string;
      name: string;
      email: string;
      transactionCount: number;
    }>
  > {
    const accountOfficers = await prisma.user.findMany({
      where: {
        role: UserRole.ACCOUNT_OFFICER,
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        _count: {
          select: {
            createdTransactions: true,
          },
        },
      },
      orderBy: {
        firstName: "asc",
      },
    });

    return accountOfficers.map((officer) => ({
      id: officer.id,
      name: `${officer.firstName} ${officer.lastName}`,
      email: officer.email,
      transactionCount: officer._count.createdTransactions,
    }));
  }

  private static getStatusInfo(
    status: string,
    currentStage?: string
  ): {
    label: string;
    color: string;
    description: string;
  } {
    switch (status) {
      case TransactionStatus.DRAFT:
        return {
          label: "Draft",
          color: "gray",
          description: "Transaction is being prepared",
        };
      case TransactionStatus.SUBMITTED:
        return {
          label: "Submitted",
          color: "blue",
          description: "Waiting for supervisor review",
        };
      case TransactionStatus.IN_PROGRESS:
        return {
          label: "In Progress",
          color: "yellow",
          description: `Currently at ${currentStage
            ?.replace("_", " ")
            .toLowerCase()}`,
        };
      case TransactionStatus.APPROVED:
        return {
          label: "Approved",
          color: "green",
          description: "Ready for disbursement",
        };
      case TransactionStatus.COMPLETED:
        return {
          label: "Disbursed",
          color: "green",
          description: "Funds have been disbursed",
        };
      case TransactionStatus.REJECTED:
        return {
          label: "Rejected",
          color: "red",
          description: "Transaction was rejected",
        };
      case TransactionStatus.SENT_BACK:
        return {
          label: "Sent Back",
          color: "orange",
          description: "Requires corrections",
        };
      default:
        return {
          label: status,
          color: "gray",
          description: "Unknown status",
        };
    }
  }

  static async getActivities(
    filters: {
      type?: string;
      dateFrom?: Date;
      dateTo?: Date;
      userId?: string;
    },
    pagination: { page: number; limit: number },
    currentUserId: string,
    currentUserRole: string
  ) {
    const { page, limit } = pagination;
    const skip = (page - 1) * limit;

    // Build activity data from multiple sources
    const activities: any[] = [];

    // 1. Transaction Activities (submissions, status changes)
    const transactionActivities = await this.getTransactionActivities(
      filters,
      currentUserId,
      currentUserRole
    );
    activities.push(...transactionActivities);

    // 2. Approval Activities
    const approvalActivities = await this.getApprovalActivities(
      filters,
      currentUserId,
      currentUserRole
    );
    activities.push(...approvalActivities);

    // Sort by timestamp (most recent first)
    activities.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Apply filters
    let filteredActivities = activities;

    if (filters.type) {
      filteredActivities = activities.filter(
        (activity) => activity.type === filters.type
      );
    }

    if (filters.dateFrom) {
      filteredActivities = filteredActivities.filter(
        (activity) => new Date(activity.timestamp) >= filters.dateFrom!
      );
    }

    if (filters.dateTo) {
      const endOfDay = new Date(filters.dateTo);
      endOfDay.setHours(23, 59, 59, 999);
      filteredActivities = filteredActivities.filter(
        (activity) => new Date(activity.timestamp) <= endOfDay
      );
    }

    if (filters.userId) {
      filteredActivities = filteredActivities.filter(
        (activity) => activity.userId === filters.userId
      );
    }

    // Pagination
    const total = filteredActivities.length;
    const paginatedActivities = filteredActivities.slice(skip, skip + limit);

    // Group by date for better UI organization
    const groupedActivities = this.groupActivitiesByDate(paginatedActivities);

    return {
      activities: groupedActivities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  private static async getTransactionActivities(
    filters: any,
    currentUserId: string,
    currentUserRole: string
  ) {
    const whereClause: any = {};

    // Role-based filtering
    if (currentUserRole === "ACCOUNT_OFFICER") {
      whereClause.createdById = currentUserId;
    }

    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
      take: 50, // Limit to recent transactions
    });

    const activities: any[] = [];

    transactions.forEach((transaction) => {
      // Transaction creation
      activities.push({
        id: `transaction-created-${transaction.id}`,
        type: "transaction_created",
        title: "Created draft transaction",
        description: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName} created a draft for new transaction`,
        timestamp: transaction.createdAt,
        userId: transaction.createdById,
        userName: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`,
        transactionId: transaction.transactionId,
        status: transaction.status,
        icon: "draft",
        color: "gray",
      });

      // Transaction submission
      if (transaction.submittedAt) {
        const customerName =
          transaction.firstName && transaction.lastName
            ? `${transaction.firstName} ${transaction.lastName}`
            : "customer";

        activities.push({
          id: `transaction-submitted-${transaction.id}`,
          type: "transaction_submitted",
          title: "Submitted loan application",
          description: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName} submitted loan application for ${customerName}`,
          timestamp: transaction.submittedAt,
          userId: transaction.createdById,
          userName: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`,
          transactionId: transaction.transactionId,
          status: transaction.status,
          icon: "submitted",
          color: "blue",
        });
      }

      // Transaction completion
      if (transaction.completedAt) {
        activities.push({
          id: `transaction-completed-${transaction.id}`,
          type: "transaction_completed",
          title: "Transaction completed",
          description: `Transaction ${transaction.transactionId} has been completed`,
          timestamp: transaction.completedAt,
          userId: transaction.createdById,
          userName: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`,
          transactionId: transaction.transactionId,
          status: transaction.status,
          icon: "completed",
          color: "green",
        });
      }
    });

    return activities;
  }

  private static async getApprovalActivities(
    filters: any,
    currentUserId: string,
    currentUserRole: string
  ) {
    const whereClause: any = {};

    // Role-based filtering for approvals
    if (currentUserRole === "ACCOUNT_OFFICER") {
      whereClause.transaction = {
        createdById: currentUserId,
      };
    }

    const approvals = await prisma.transactionApproval.findMany({
      where: whereClause,
      include: {
        approver: {
          select: {
            firstName: true,
            lastName: true,
            role: true,
          },
        },
        transaction: {
          select: {
            transactionId: true,
            firstName: true,
            lastName: true,
            createdBy: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 50,
    });

    return approvals.map((approval) => {
      const customerName =
        approval.transaction.firstName && approval.transaction.lastName
          ? `${approval.transaction.firstName} ${approval.transaction.lastName}`
          : "customer";

      let title = "";
      let description = "";
      let icon = "";
      let color = "";

      switch (approval.action) {
        case "APPROVED":
          title = "Approved loan application";
          description = `${approval.approver.firstName} ${approval.approver.lastName} approved loan application for ${customerName}`;
          icon = "approved";
          color = "green";
          break;
        case "REJECTED":
          title = "Rejected loan request";
          description = `${approval.approver.firstName} ${approval.approver.lastName} rejected loan request for ${customerName}`;
          icon = "rejected";
          color = "red";
          break;
        case "SENT_BACK":
          title = "Sent back for correction";
          description = `Transaction ${approval.transaction.transactionId} sent back for correction`;
          icon = "sent_back";
          color = "orange";
          break;
        case "DISBURSED":
          title = "Funds disbursed";
          description = `${approval.approver.firstName} ${approval.approver.lastName} disbursed funds for ${customerName}`;
          icon = "disbursed";
          color = "green";
          break;
        case "SUBMITTED":
          title = "Submitted for approval";
          description = `${approval.approver.firstName} ${approval.approver.lastName} submitted transaction for approval`;
          icon = "submitted";
          color = "blue";
          break;
        default:
          title = "Approval action";
          description = `${approval.approver.firstName} ${approval.approver.lastName} performed ${approval.action} on transaction`;
          icon = "action";
          color = "gray";
      }

      return {
        id: `approval-${approval.id}`,
        type: "approval_action",
        title,
        description,
        timestamp: approval.createdAt,
        userId: approval.approverId,
        userName: `${approval.approver.firstName} ${approval.approver.lastName}`,
        userRole: approval.approver.role,
        transactionId: approval.transaction.transactionId,
        action: approval.action,
        comments: approval.comments,
        icon,
        color,
      };
    });
  }

  private static groupActivitiesByDate(activities: any[]) {
    const grouped: { [key: string]: any[] } = {};
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    activities.forEach((activity) => {
      const activityDate = new Date(activity.timestamp);
      let dateKey = "";

      if (this.isSameDay(activityDate, today)) {
        dateKey = "Today";
      } else if (this.isSameDay(activityDate, yesterday)) {
        dateKey = "Yesterday";
      } else {
        dateKey = activityDate.toLocaleDateString("en-US", {
          weekday: "long",
          month: "short",
          day: "numeric",
          year: "numeric",
        });
      }

      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push({
        ...activity,
        timeAgo: this.getTimeAgo(activityDate),
      });
    });

    // Convert to array format for easier frontend consumption
    return Object.entries(grouped).map(([date, activities]) => ({
      date,
      activities,
    }));
  }

  private static isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  private static getTimeAgo(date: Date): string {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return "Just now";
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} min${minutes > 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? "s" : ""} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? "s" : ""} ago`;
    }
  }

  static async getUserManagement(
    filters: {
      role?: string;
      status?: string;
      search?: string;
      dateFrom?: Date;
      dateTo?: Date;
    },
    pagination: { page: number; limit: number }
  ) {
    const { page, limit } = pagination;
    const skip = (page - 1) * limit;

    // Build where clause for filtering
    const whereClause: any = {};

    // Role filter
    if (filters.role) {
      whereClause.role = filters.role;
    }

    // Status filter (active/inactive)
    if (filters.status) {
      whereClause.isActive = filters.status === "active";
    }

    // Search filter (name or email)
    if (filters.search) {
      whereClause.OR = [
        {
          firstName: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          lastName: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          email: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
      ];
    }

    // Date filter
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};
      if (filters.dateFrom) {
        whereClause.createdAt.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        const endDate = new Date(filters.dateTo);
        endDate.setDate(endDate.getDate() + 1);
        whereClause.createdAt.lt = endDate;
      }
    }

    // Get users with transaction counts and last login info
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          isActive: true,
          createdAt: true,
          lastLogin: true,
          monthlyTarget: true,
          _count: {
            select: {
              createdTransactions: true,
              approvals: true,
            },
          },
        },
        orderBy: [
          { isActive: "desc" }, // Active users first
          { createdAt: "desc" }, // Then by creation date
        ],
        skip,
        take: limit,
      }),
      prisma.user.count({ where: whereClause }),
    ]);

    // Format users with additional information
    const formattedUsers = users.map((user) => {
      const permissions = this.getUserPermissions(user.role);
      const lastActive = user.lastLogin || user.createdAt;

      return {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        roleLabel: this.getRoleLabel(user.role),
        isActive: user.isActive,
        status: user.isActive ? "Active" : "Disabled",
        permissions,
        permissionLabels: permissions.map((p) => this.getPermissionLabel(p)),
        lastActive: lastActive,
        lastActiveFormatted: this.formatDateTime(lastActive),
        dateAdded: user.createdAt,
        dateAddedFormatted: this.formatDateTime(user.createdAt),
        monthlyTarget: user.monthlyTarget,
        transactionCount: user._count.createdTransactions,
        approvalCount: user._count.approvals,
        canEdit: true, // Super admin can edit all users
        canDisable: user.role !== "SUPER_ADMIN", // Cannot disable other super admins
      };
    });

    // Get role statistics
    const roleStats = await this.getUserRoleStatistics();

    return {
      users: formattedUsers,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
      statistics: {
        totalUsers: totalCount,
        activeUsers: formattedUsers.filter((u) => u.isActive).length,
        inactiveUsers: formattedUsers.filter((u) => !u.isActive).length,
        roleBreakdown: roleStats,
      },
    };
  }

  private static getUserPermissions(role: string): string[] {
    const permissions: { [key: string]: string[] } = {
      SUPER_ADMIN: [
        "admin",
        "create_user",
        "edit_user",
        "disable_user",
        "view_all",
        "system_settings",
      ],
      ACCOUNT_OFFICER: [
        "create_transaction",
        "edit_own_transaction",
        "upload_documents",
      ],
      SUPERVISOR: ["submit", "send_back", "view_team_transactions"],
      HEAD_CONSUMER_LENDING: [
        "approve",
        "reject",
        "send_back",
        "view_all_transactions",
      ],
      HEAD_RISK_MANAGEMENT: [
        "approve",
        "reject",
        "send_back",
        "risk_assessment",
      ],
      MANAGING_DIRECTOR: ["approve", "reject", "send_back", "final_approval"],
      ACCOUNTANT: ["disburse", "send_back", "financial_records"],
    };

    return permissions[role] || [];
  }

  private static getPermissionLabel(permission: string): string {
    const labels: { [key: string]: string } = {
      admin: "Admin",
      create_user: "Create User",
      edit_user: "Edit User",
      disable_user: "Disable User",
      view_all: "View All",
      system_settings: "System Settings",
      create_transaction: "Create transaction",
      edit_own_transaction: "Edit Own Transaction",
      upload_documents: "Upload Documents",
      submit: "Submit",
      send_back: "Send back",
      view_team_transactions: "View Team Transactions",
      approve: "Approve",
      reject: "Reject",
      view_all_transactions: "View All Transactions",
      risk_assessment: "Risk Assessment",
      final_approval: "Final Approval",
      disburse: "Disburse",
      financial_records: "Financial Records",
    };

    return labels[permission] || permission;
  }

  private static getRoleLabel(role: string): string {
    const labels: { [key: string]: string } = {
      SUPER_ADMIN: "Super Admin",
      ACCOUNT_OFFICER: "Account Officer",
      SUPERVISOR: "Supervisor",
      HEAD_CONSUMER_LENDING: "Head Consumer Lending",
      HEAD_RISK_MANAGEMENT: "Head Risk Management",
      MANAGING_DIRECTOR: "Managing Director",
      ACCOUNTANT: "Accountant",
    };

    return labels[role] || role;
  }

  private static async getUserRoleStatistics() {
    const roleStats = await prisma.user.groupBy({
      by: ["role"],
      _count: {
        role: true,
      },
      where: {
        isActive: true,
      },
    });

    return roleStats.map((stat) => ({
      role: stat.role,
      roleLabel: this.getRoleLabel(stat.role),
      count: stat._count.role,
    }));
  }

  static async getPerformanceOverview(): Promise<{
    wellPerformedPercentage: number;
    performers: Array<{
      id: string;
      name: string;
      initials: string;
      approvedAmount: number;
      monthlyTarget: number;
      achievementPercentage: number;
      transactionCount: number;
    }>;
  }> {
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    // Get all account officers
    const accountOfficers = await prisma.user.findMany({
      where: {
        role: UserRole.ACCOUNT_OFFICER,
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        monthlyTarget: true,
      },
    });

    // Get transactions approved by Head Risk (final approval) for current month
    // These are transactions with APPROVED status at ACCOUNTANT stage (ready for disbursement)
    const approvedTransactions = await prisma.transaction.groupBy({
      by: ["createdById"],
      where: {
        status: TransactionStatus.APPROVED,
        currentStage: ApprovalStage.ACCOUNTANT, // Final approval stage
        createdAt: {
          gte: currentMonth,
          lt: nextMonth,
        },
        createdById: {
          in: accountOfficers.map((ao) => ao.id),
        },
      },
      _sum: {
        requestedAmount: true,
      },
      _count: {
        id: true,
      },
    });

    // Create a map for quick lookup
    const transactionMap = new Map(
      approvedTransactions.map((t) => [
        t.createdById,
        {
          amount: t._sum.requestedAmount || 0,
          count: t._count.id,
        },
      ])
    );

    // Calculate performance for each account officer based on approved transactions (final approval by Head Risk)
    const performers = accountOfficers.map((officer) => {
      const transactionData = transactionMap.get(officer.id) || {
        amount: 0,
        count: 0,
      };
      const monthlyTarget = officer.monthlyTarget || 1000000; // Default 1M if not set
      const achievementPercentage = Math.round(
        (transactionData.amount / monthlyTarget) * 100
      );

      return {
        id: officer.id,
        name: `${officer.firstName} ${officer.lastName}`,
        initials: this.generateInitials(
          `${officer.firstName} ${officer.lastName}`
        ),
        approvedAmount: transactionData.amount, // Amount from transactions approved by Head Risk
        monthlyTarget,
        achievementPercentage,
        transactionCount: transactionData.count, // Count of transactions approved by Head Risk
      };
    });

    // Calculate well-performed percentage (80% threshold)
    const wellPerformed = performers.filter(
      (p) => p.achievementPercentage >= 80
    );
    const wellPerformedPercentage =
      performers.length > 0
        ? Math.round((wellPerformed.length / performers.length) * 100)
        : 0;

    // Sort by achievement percentage (descending)
    performers.sort(
      (a, b) => b.achievementPercentage - a.achievementPercentage
    );

    return {
      wellPerformedPercentage,
      performers,
    };
  }

  private static generateInitials(fullName: string): string {
    return fullName
      .split(" ")
      .map((name) => name.charAt(0).toUpperCase())
      .join("")
      .substring(0, 2);
  }

  private static formatDateTime(date: Date): string {
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      })
      .replace(",", "");
  }
}
