import { Request } from 'express';
import { UserRole, TransactionStatus, ApprovalStage } from '@prisma/client';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    firstName: string;
    lastName: string;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TransactionFilters {
  status?: TransactionStatus;
  stage?: ApprovalStage;
  createdById?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export interface DashboardStats {
  totalTransactions: number;
  pendingTransactions: number;
  approvedTransactions: number;
  rejectedTransactions: number;
  totalAmount: number;
  successRate: number;
  monthlyTarget?: number;
  monthlyProgress?: number;
}

export interface NotificationData {
  userId: string;
  transactionId?: string;
  type: string;
  title: string;
  message: string;
}

export interface FileUploadResult {
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
}
