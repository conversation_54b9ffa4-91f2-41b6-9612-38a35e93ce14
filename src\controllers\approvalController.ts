import { Response } from "express";
import { ApprovalService } from "../services/approvalService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";

export class ApprovalController {
  static processApproval = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const { action, comments } = req.body;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      await ApprovalService.processApproval(transactionId, userId, userRole, {
        action,
        comments,
      });

      ResponseHandler.success(
        res,
        `Transaction ${action.toLowerCase()} successfully`
      );
    }
  );

  static getTransactionsForApproval = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userRole = req.user!.role;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result = await ApprovalService.getTransactionsForApproval(
        userRole,
        page,
        limit
      );

      ResponseHandler.success(
        res,
        "Transactions for approval retrieved successfully",
        result
      );
    }
  );

  static getApprovalHistory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;

      const history = await ApprovalService.getApprovalHistory(transactionId);

      ResponseHandler.success(
        res,
        "Approval history retrieved successfully",
        history
      );
    }
  );

  static getPendingApprovals = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userRole = req.user!.role;
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const accountOfficerId = req.query.accountOfficerId as string;
      const loanType = req.query.loanType as string;
      const dateFrom = req.query.dateFrom as string;
      const dateTo = req.query.dateTo as string;
      const search = req.query.search as string;
      const exportCsv = (req.query.export as string) === "true";

      // Handle multiple account officer IDs
      let accountOfficerIds: string[] | undefined;
      if (req.query.accountOfficerIds) {
        if (typeof req.query.accountOfficerIds === "string") {
          accountOfficerIds = (req.query.accountOfficerIds as string)
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id.length > 0);
        } else if (Array.isArray(req.query.accountOfficerIds)) {
          accountOfficerIds = req.query.accountOfficerIds as string[];
        }
      }

      const filters = {
        page,
        limit,
        accountOfficerId,
        accountOfficerIds,
        loanType,
        dateFrom,
        dateTo,
        search,
      };

      // Handle XLSX export
      if (exportCsv) {
        console.log("XLSX export request:", {
          userRole,
          userId,
          filters,
        });

        try {
          console.log("🔄 Starting XLSX export process...");

          // Generate XLSX content
          const xlsxBuffer = await ApprovalService.exportPendingApprovalsXLSX(
            userRole,
            userId,
            filters
          );

          console.log("✅ XLSX buffer generated:", {
            bufferLength: xlsxBuffer?.length,
            isBuffer: Buffer.isBuffer(xlsxBuffer),
            bufferType: typeof xlsxBuffer,
          });

          // Validate XLSX buffer
          if (!xlsxBuffer || !Buffer.isBuffer(xlsxBuffer)) {
            console.error("❌ Invalid XLSX buffer:", typeof xlsxBuffer);
            return ResponseHandler.error(
              res,
              "Failed to generate XLSX content",
              undefined,
              500
            );
          }

          // Generate filename
          const filename = ApprovalService.generatePendingApprovalsXLSXFilename(
            userRole,
            filters
          );

          // FORCE XLSX HEADERS - NO CSV ALLOWED!
          console.log("🎯 Setting XLSX headers for file:", filename);

          res.setHeader(
            "Content-Type",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          );
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${filename}"`
          );
          res.setHeader("Content-Length", xlsxBuffer.length);
          res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
          res.setHeader("Pragma", "no-cache");
          res.setHeader("Expires", "0");

          // Double-check filename has .xlsx extension
          if (!filename.endsWith(".xlsx")) {
            console.error("🚨 FILENAME ERROR: Not .xlsx extension!", filename);
          }

          console.log("XLSX export successful:", {
            userRole,
            filename,
            contentLength: xlsxBuffer.length,
            filters,
          });

          return res.send(xlsxBuffer);
        } catch (error) {
          console.error("❌ XLSX export error:", error);
          return ResponseHandler.error(
            res,
            "Failed to generate XLSX export",
            error instanceof Error ? error.message : "Unknown error",
            500
          );
        }
      }

      // Handle regular JSON response
      const result = await ApprovalService.getPendingApprovals(
        userRole,
        userId,
        filters
      );

      ResponseHandler.success(
        res,
        "Pending approvals retrieved successfully",
        result
      );
    }
  );

  static getMyRequestTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const transaction = await ApprovalService.getMyRequestTransaction(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transaction retrieved successfully",
        transaction
      );
    }
  );

  /**
   * NEW ENDPOINT: Streaming XLSX export - Memory Optimized
   */
  static exportPendingApprovalsXLSX = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userRole = req.user!.role;
      const userId = req.user!.id;
      const accountOfficerId = req.query.accountOfficerId as string;
      const loanType = req.query.loanType as string;
      const dateFrom = req.query.dateFrom as string;
      const dateTo = req.query.dateTo as string;
      const search = req.query.search as string;
      const useStreaming = req.query.streaming !== "false"; // Default to streaming

      console.log("🚀 STREAMING XLSX ENDPOINT CALLED - MEMORY OPTIMIZED!");

      // Handle multiple account officer IDs
      let accountOfficerIds: string[] | undefined;
      if (req.query.accountOfficerIds) {
        if (typeof req.query.accountOfficerIds === "string") {
          accountOfficerIds = (req.query.accountOfficerIds as string)
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id.length > 0);
        } else if (Array.isArray(req.query.accountOfficerIds)) {
          accountOfficerIds = req.query.accountOfficerIds as string[];
        }
      }

      const filters = {
        accountOfficerId,
        accountOfficerIds,
        loanType,
        dateFrom,
        dateTo,
        search,
      };

      try {
        if (useStreaming) {
          console.log("🌊 Using streaming XLSX export (Memory Optimized)...");

          // Import streaming service dynamically to avoid circular dependencies
          const { StreamingExportService } = await import(
            "../services/streamingExportService"
          );

          const result =
            await StreamingExportService.exportPendingApprovalsStreaming(
              userRole,
              userId,
              filters
            );

          console.log("✅ STREAMING XLSX generated:", {
            filename: result.filename,
            totalRows: result.totalRows,
            filePath: result.filePath,
          });

          // Set XLSX headers
          res.setHeader(
            "Content-Type",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          );
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${result.filename}"`
          );
          res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
          res.setHeader("Pragma", "no-cache");
          res.setHeader("Expires", "0");

          console.log("🎯 STREAMING - Sending XLSX file:", result.filename);

          // Stream the file to response
          const fs = await import("fs");
          const fileStream = fs.createReadStream(result.filePath);

          fileStream.on("end", () => {
            console.log("✅ File streaming completed");
            // Clean up file after streaming (optional)
            setTimeout(() => {
              if (fs.existsSync(result.filePath)) {
                fs.unlinkSync(result.filePath);
                console.log("🗑️ Temporary file cleaned up");
              }
            }, 5000); // Clean up after 5 seconds
          });

          fileStream.on("error", (error) => {
            console.error("❌ File streaming error:", error);
          });

          return fileStream.pipe(res);
        } else {
          console.log("🔄 Using legacy XLSX export (High Memory)...");

          // Fallback to original method
          const xlsxBuffer = await ApprovalService.exportPendingApprovalsXLSX(
            userRole,
            userId,
            filters
          );

          const timestamp = Date.now();
          const filename = `pending-approvals-${userRole.toLowerCase()}-${timestamp}.xlsx`;

          console.log("✅ LEGACY XLSX generated:", {
            filename,
            size: xlsxBuffer.length,
            isBuffer: Buffer.isBuffer(xlsxBuffer),
          });

          res.setHeader(
            "Content-Type",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          );
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${filename}"`
          );
          res.setHeader("Content-Length", xlsxBuffer.length);
          res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
          res.setHeader("Pragma", "no-cache");
          res.setHeader("Expires", "0");

          return res.send(xlsxBuffer);
        }
      } catch (error) {
        console.error("❌ XLSX export error:", error);
        return ResponseHandler.error(
          res,
          "Failed to generate XLSX export",
          error instanceof Error ? error.message : "Unknown error",
          500
        );
      }
    }
  );
}
