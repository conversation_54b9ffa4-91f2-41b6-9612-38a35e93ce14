import { Response } from "express";
import { TransactionService } from "../services/transactionService";
import { ApprovalService } from "../services/approvalService";
import { PDFService } from "../services/pdfService";
import { ZipService } from "../services/zipService";
import { CSVService } from "../services/csvService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";
import { LOAN_TYPE_CONFIGS } from "../types/loanTypes";
import { LoanEligibilityService } from "../services/loanEligibilityService";
import prisma from "../db/db";
import { TransactionStatus } from "@prisma/client";

export class TransactionController {
  static createTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.id;

      const result = await TransactionService.createTransaction(userId);

      ResponseHandler.success(
        res,
        "Transaction created successfully",
        result,
        201
      );
    }
  );

  static updatePersonalInfo = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user?.id;
      const data = req.body;

      console.log("Controller updatePersonalInfo called:", {
        transactionId,
        userId,
        userExists: !!req.user,
        bodyKeys: Object.keys(data || {}),
      });

      if (!userId) {
        console.error("No user ID found in request");
        return ResponseHandler.error(
          res,
          "User not authenticated",
          undefined,
          401
        );
      }

      if (!transactionId) {
        console.error("No transaction ID provided");
        return ResponseHandler.error(
          res,
          "Transaction ID is required",
          undefined,
          400
        );
      }

      await TransactionService.updatePersonalInfo(transactionId, data, userId);

      ResponseHandler.success(res, "Personal information updated successfully");
    }
  );

  static updateNextOfKin = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.updateNextOfKin(transactionId, data, userId);

      ResponseHandler.success(
        res,
        "Next of kin information updated successfully"
      );
    }
  );

  static updateLoanInfo = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.updateLoanInfo(transactionId, data, userId);

      ResponseHandler.success(res, "Loan information updated successfully");
    }
  );

  static updateDisbursement = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.updateDisbursement(transactionId, data, userId);

      ResponseHandler.success(
        res,
        "Disbursement information updated successfully"
      );
    }
  );

  static submitTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;

      console.log("Submit transaction request:", { transactionId, userId });

      try {
        await TransactionService.submitTransaction(transactionId, userId);
        ResponseHandler.success(res, "Transaction submitted successfully");
      } catch (error: any) {
        console.error("Submit transaction error:", {
          transactionId,
          userId,
          error: error.message,
          isOperational: error.isOperational,
          statusCode: error.statusCode,
        });
        throw error; // Re-throw to let error handler handle it
      }
    }
  );

  static getTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const transaction = await TransactionService.getTransaction(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transaction retrieved successfully",
        transaction
      );
    }
  );

  static getTransactions = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const filters = {
        status: req.query.status as any,
        stage: req.query.stage as any,
        createdById: req.query.createdById as string,
        dateFrom: req.query.dateFrom
          ? new Date(req.query.dateFrom as string)
          : undefined,
        dateTo: req.query.dateTo
          ? new Date(req.query.dateTo as string)
          : undefined,
        search: req.query.search as string,
      };

      const pagination = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        sortBy: (req.query.sortBy as string) || "createdAt",
        sortOrder: (req.query.sortOrder as "asc" | "desc") || "desc",
      };

      const result = await TransactionService.getTransactions(
        filters,
        pagination,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transactions retrieved successfully",
        result
      );
    }
  );

  static deleteTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;

      await TransactionService.deleteTransaction(transactionId, userId);

      ResponseHandler.success(res, "Transaction deleted successfully");
    }
  );

  static setLoanType = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.setLoanType(transactionId, data, userId);

      ResponseHandler.success(res, "Loan type set successfully");
    }
  );

  static getTransactionPreview = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transaction preview retrieved successfully",
        transaction
      );
    }
  );

  static downloadTransactionPDF = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      // Get transaction data
      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      // Generate PDF
      const pdfBuffer = await PDFService.generateTransactionPDF(transaction);

      // Set response headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="transaction-${transaction.transactionId}.pdf"`
      );
      res.setHeader("Content-Length", pdfBuffer.length);

      res.send(pdfBuffer);
    }
  );

  static downloadTransactionCSV = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log("CSV export request:", { transactionId, userId, userRole });

      // Get transaction data
      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      // Generate CSV content
      const csvContent = CSVService.generateTransactionCSV(transaction);
      const filename = CSVService.generateCSVFilename(
        transaction.transactionId
      );

      // Set response headers for CSV download
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );
      res.setHeader("Content-Length", Buffer.byteLength(csvContent, "utf8"));

      console.log("CSV export successful:", {
        transactionId,
        filename,
        contentLength: Buffer.byteLength(csvContent, "utf8"),
      });

      res.send(csvContent);
    }
  );

  static getLoanTypes = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const loanTypes = Object.values(LOAN_TYPE_CONFIGS);

      ResponseHandler.success(
        res,
        "Loan types retrieved successfully",
        loanTypes
      );
    }
  );

  static downloadAll = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      try {
        // Validate transaction and check access
        const validation = await ZipService.validateTransactionForZip(
          transactionId,
          userId,
          userRole
        );

        if (!validation.canAccess) {
          return ResponseHandler.error(
            res,
            "Transaction not found or access denied",
            undefined,
            404
          );
        }

        if (!validation.hasContent) {
          return ResponseHandler.error(
            res,
            "No content available for download",
            undefined,
            404
          );
        }

        // Get estimated file size for logging
        const estimatedSize = await ZipService.getEstimatedZipSize(
          transactionId,
          userId,
          userRole
        );

        console.log(
          `Creating ZIP for transaction ${transactionId}, estimated size: ${estimatedSize} bytes`
        );

        // Create ZIP archive
        const { zipBuffer, fileName } = await ZipService.createTransactionZip(
          transactionId,
          userId,
          userRole
        );

        // Set appropriate headers for ZIP download
        res.setHeader("Content-Type", "application/zip");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="${fileName}"`
        );
        res.setHeader("Content-Length", zipBuffer.length.toString());
        res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        res.setHeader("Pragma", "no-cache");
        res.setHeader("Expires", "0");

        // Send the ZIP file
        res.send(zipBuffer);

        console.log(
          `ZIP download completed for transaction ${transactionId}, actual size: ${zipBuffer.length} bytes`
        );
      } catch (error) {
        console.error(
          `Error creating ZIP for transaction ${transactionId}:`,
          error
        );

        // Check if response has already been sent
        if (!res.headersSent) {
          ResponseHandler.error(
            res,
            error instanceof Error
              ? error.message
              : "Failed to create ZIP archive",
            undefined,
            500
          );
        }
      }
    }
  );

  static getApprovalHistory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      // Check if user has permission to view this transaction's approval history
      // For now, allow all authenticated users to view approval history
      // You can add more specific authorization logic here if needed

      const approvalHistory = await ApprovalService.getDetailedApprovalHistory(
        transactionId
      );

      ResponseHandler.success(
        res,
        "Approval history retrieved successfully",
        approvalHistory
      );
    }
  );

  static checkLoanEligibility = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { bvn } = req.body;

      if (!bvn) {
        return ResponseHandler.error(res, "BVN is required", undefined, 400);
      }

      if (!/^\d{11}$/.test(bvn)) {
        return ResponseHandler.error(
          res,
          "BVN must be exactly 11 digits",
          undefined,
          400
        );
      }

      const eligibilityResult =
        await LoanEligibilityService.checkLoanEligibilityByBVN(bvn);

      ResponseHandler.success(
        res,
        eligibilityResult.isEligible
          ? "You are eligible for a new loan"
          : "You are not eligible for a new loan at this time",
        eligibilityResult
      );
    }
  );

  static getLoanHistory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { bvn } = req.params;

      if (!bvn) {
        return ResponseHandler.error(res, "BVN is required", undefined, 400);
      }

      if (!/^\d{11}$/.test(bvn)) {
        return ResponseHandler.error(
          res,
          "BVN must be exactly 11 digits",
          undefined,
          400
        );
      }

      const loanHistory = await LoanEligibilityService.getLoanHistoryByBVN(bvn);

      ResponseHandler.success(
        res,
        "Loan history retrieved successfully",
        loanHistory
      );
    }
  );

  static markLoanCompleted = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const { completionReason } = req.body;

      await LoanEligibilityService.markLoanAsCompleted(
        transactionId,
        completionReason
      );

      ResponseHandler.success(res, "Loan marked as completed successfully");
    }
  );

  static markLoanRepaid = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const { repaymentMethod, repaymentReference, notes } = req.body;

      await LoanEligibilityService.markLoanAsRepaid(transactionId, {
        repaymentMethod,
        repaymentReference,
        notes,
      });

      ResponseHandler.success(res, "Loan marked as repaid successfully");
    }
  );

  static getTransactionStatus = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user?.id;

      console.log("Getting transaction status:", { transactionId, userId });

      if (!userId) {
        return ResponseHandler.error(
          res,
          "User not authenticated",
          undefined,
          401
        );
      }

      if (!transactionId || transactionId.length !== 24) {
        return ResponseHandler.error(
          res,
          "Invalid transaction ID format",
          undefined,
          400
        );
      }

      const transaction = await prisma.transaction.findFirst({
        where: { id: transactionId, createdById: userId },
        select: {
          status: true,
          transactionId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!transaction) {
        console.log("Transaction not found for status check:", {
          transactionId,
          userId,
        });
        return ResponseHandler.error(
          res,
          "Transaction not found",
          undefined,
          404
        );
      }

      console.log("Transaction status found:", transaction);

      ResponseHandler.success(res, "Transaction status retrieved", {
        status: transaction.status,
        transactionId: transaction.transactionId,
        canEdit: transaction.status === TransactionStatus.DRAFT,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
      });
    }
  );

  static validateTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user?.id;

      console.log("Validating transaction:", { transactionId, userId });

      if (!userId) {
        return ResponseHandler.error(
          res,
          "User not authenticated",
          undefined,
          401
        );
      }

      if (!transactionId || transactionId.length !== 24) {
        return ResponseHandler.error(
          res,
          "Invalid transaction ID format",
          undefined,
          400
        );
      }

      const transaction = await prisma.transaction.findFirst({
        where: { id: transactionId, createdById: userId },
        include: { documents: true },
      });

      if (!transaction) {
        return ResponseHandler.error(
          res,
          "Transaction not found",
          undefined,
          404
        );
      }

      // Get validation details using the new method
      const validationResult = await TransactionService.getValidationDetails(
        transaction
      );

      ResponseHandler.success(res, "Transaction validation completed", {
        isComplete: validationResult.isComplete,
        missingFields: validationResult.missingFields,
        missingDocuments: validationResult.missingDocuments,
        canSubmit:
          validationResult.isComplete &&
          (transaction.status === TransactionStatus.DRAFT ||
            transaction.status === TransactionStatus.SENT_BACK),
        status: transaction.status,
        transactionId: transaction.transactionId,
      });
    }
  );
}
