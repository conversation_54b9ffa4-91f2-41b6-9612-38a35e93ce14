import { Router } from "express";
import { StreamingExportService } from "../services/streamingExportService";
import { WorkerManager } from "../services/workerManager";
import { NonBlockingCron } from "../services/nonBlockingCron";
import { UserRole } from "@prisma/client";
import { ResponseHandler } from "../utils/response";

const router = Router();

/**
 * Test streaming XLSX export
 */
router.get("/streaming-xlsx", async (req, res) => {
  try {
    console.log("🧪 Testing streaming XLSX export...");

    const result = await StreamingExportService.exportPendingApprovalsStreaming(
      UserRole.SUPERVISOR, // Test with supervisor role
      "test-user-id",
      {
        // Add test filters if needed
      }
    );

    return ResponseHandler.success(res, "Streaming XLSX test completed", {
      filename: result.filename,
      totalRows: result.totalRows,
      filePath: result.filePath,
    });
  } catch (error) {
    console.error("❌ Streaming XLSX test failed:", error);
    return ResponseHandler.error(
      res,
      "Streaming XLSX test failed",
      error instanceof Error ? error.message : "Unknown error"
    );
  }
});

/**
 * Test worker thread export
 */
router.get("/worker-export", async (req, res) => {
  try {
    console.log("🧪 Testing worker thread export...");

    const result = await WorkerManager.runExportInWorker(
      UserRole.SUPERVISOR,
      "test-user-id",
      {}
    );

    return ResponseHandler.success(res, "Worker export test completed", result);
  } catch (error) {
    console.error("❌ Worker export test failed:", error);
    return ResponseHandler.error(
      res,
      "Worker export test failed",
      error instanceof Error ? error.message : "Unknown error"
    );
  }
});

/**
 * Get cron jobs status
 */
router.get("/cron-status", (req, res) => {
  try {
    const status = NonBlockingCron.getJobsStatus();
    return ResponseHandler.success(res, "Cron status retrieved", status);
  } catch (error) {
    console.error("❌ Cron status error:", error);
    return ResponseHandler.error(
      res,
      "Failed to get cron status",
      error instanceof Error ? error.message : "Unknown error"
    );
  }
});

/**
 * Test memory monitoring
 */
router.get("/memory-status", (req, res) => {
  try {
    const memUsage = process.memoryUsage();
    const activeWorkers = WorkerManager.getActiveWorkerCount();

    const status = {
      timestamp: new Date().toISOString(),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
        external: Math.round(memUsage.external / 1024 / 1024) + "MB",
      },
      activeWorkers,
      uptime: Math.round(process.uptime()) + "s",
    };

    return ResponseHandler.success(res, "Memory status retrieved", status);
  } catch (error) {
    console.error("❌ Memory status error:", error);
    return ResponseHandler.error(
      res,
      "Failed to get memory status",
      error instanceof Error ? error.message : "Unknown error"
    );
  }
});

export default router;
