import prisma from "../db/db";
import { NotificationService } from "./notificationService";
// REMOVED: EmailService import - transaction emails disabled
import { FileUploadUtils } from "../utils/fileUpload";
import { TransactionStatus, ApprovalStage, UserRole } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";
import * as XLSX from "xlsx";

export interface ApprovalAction {
  action:
    | "APPROVED"
    | "REJECTED"
    | "SENT_BACK"
    | "DISBURSED"
    | "SUBMITTED"
    | "LOAN_REPAID";
  comments?: string;
}

export class ApprovalService {
  // Define role to stage mapping
  private static readonly ROLE_TO_STAGE: Record<
    UserRole,
    ApprovalStage | null
  > = {
    [UserRole.SUPER_ADMIN]: null,
    [UserRole.ACCOUNT_OFFICER]: ApprovalStage.ACCOUNT_OFFICER,
    [UserRole.SUPERVISOR]: ApprovalStage.SUPERVISOR,
    [UserRole.HEAD_CONSUMER_LENDING]: ApprovalStage.HEAD_CONSUMER_LENDING,
    [UserRole.HEAD_RISK_MANAGEMENT]: ApprovalStage.HEAD_RISK_MANAGEMENT,
    [UserRole.MANAGING_DIRECTOR]: null, // MANAGING_DIRECTOR removed from approval workflow
    [UserRole.ACCOUNTANT]: ApprovalStage.ACCOUNTANT,
  };

  static async processApproval(
    transactionId: string,
    approverId: string,
    userRole: UserRole,
    action: ApprovalAction
  ): Promise<void> {
    console.log(
      `🔍 Processing approval: ${action.action} by ${userRole} for transaction ${transactionId}`
    );
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        createdBy: true,
        approvals: true,
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "The transaction you're trying to approve could not be found. Please verify the transaction ID and try again.",
        404
      );
    }

    console.log(
      `📋 Transaction state: ${transaction.status}, Current stage: ${transaction.currentStage}`
    );

    // Check if transaction is in a state that can be processed
    const validStatuses: TransactionStatus[] = [
      TransactionStatus.SUBMITTED,
      TransactionStatus.IN_PROGRESS,
    ];

    // For accountants, also allow APPROVED status for disbursement
    if (userRole === UserRole.ACCOUNTANT) {
      validStatuses.push(TransactionStatus.APPROVED);
    }

    if (!validStatuses.includes(transaction.status)) {
      throw new OperationalError(
        `This transaction cannot be approved because it has a status of '${transaction.status}'. Only transactions with 'Submitted', 'In Progress', or 'Approved' status can be processed at this stage.`,
        400
      );
    }

    // Verify user has permission to approve at current stage
    const requiredStage = this.ROLE_TO_STAGE[userRole];
    if (!requiredStage) {
      throw new OperationalError(
        `Your role (${userRole}) is not authorized to perform approval actions.`,
        403
      );
    }

    // Users can only act at their current stage
    if (transaction.currentStage !== requiredStage) {
      throw new OperationalError(
        "You do not have permission to approve this transaction at its current stage",
        403
      );
    }

    // Validate action based on user role
    const validActionsForRole: Record<UserRole, string[]> = {
      [UserRole.ACCOUNT_OFFICER]: [], // Account officers don't perform approval actions
      [UserRole.SUPERVISOR]: ["SUBMITTED", "SENT_BACK"],
      [UserRole.HEAD_CONSUMER_LENDING]: ["APPROVED", "REJECTED", "SENT_BACK"],
      [UserRole.HEAD_RISK_MANAGEMENT]: ["APPROVED", "REJECTED", "SENT_BACK"],
      [UserRole.MANAGING_DIRECTOR]: [], // MANAGING_DIRECTOR removed from approval workflow
      [UserRole.ACCOUNTANT]: ["DISBURSED", "LOAN_REPAID", "SENT_BACK"],
      [UserRole.SUPER_ADMIN]: [], // Super admins don't perform approval actions directly
    };

    const allowedActions = validActionsForRole[userRole];
    if (!allowedActions.includes(action.action)) {
      const error = new Error(
        `${userRole} cannot perform ${
          action.action
        } action. Allowed actions: ${allowedActions.join(", ")}`
      );
      (error as any).statusCode = 400;
      (error as any).isOperational = true;
      throw error;
    }

    // Check if an approval already exists for this transaction at this stage
    const existingApproval = await prisma.transactionApproval.findUnique({
      where: {
        transactionId_stage: {
          transactionId: transaction.id,
          stage: requiredStage,
        },
      },
    });

    // If approval exists, check if it's by the same user and if transaction has been sent back since
    if (existingApproval) {
      // If it's a different user trying to act at the same stage, block it
      if (existingApproval.approverId !== approverId) {
        throw new OperationalError(
          "Another user has already acted on this transaction at this stage. Only one user can perform actions at each approval stage.",
          409 // Conflict status code
        );
      }

      // If same user, check if transaction was resubmitted after their last action
      // This allows re-action after send-back and resubmission cycles
      const wasResubmittedAfterApproval =
        transaction.submittedAt &&
        new Date(transaction.submittedAt) >
          new Date(existingApproval.createdAt);

      // If transaction wasn't resubmitted after user's action, they can't act again
      if (!wasResubmittedAfterApproval) {
        throw new OperationalError(
          "You have already performed an action on this transaction at this stage. You can only act again if the transaction is sent back and resubmitted.",
          409 // Conflict status code
        );
      }

      console.log(
        `✅ User ${approverId} can act again on transaction ${transaction.transactionId} - resubmitted after their last action:`,
        {
          lastApprovalDate: existingApproval.createdAt,
          resubmittedAt: transaction.submittedAt,
          stage: requiredStage,
        }
      );
    }

    // Process the approval action
    await this.executeApprovalAction(
      transaction,
      approverId,
      requiredStage,
      action
    );
  }

  private static async executeApprovalAction(
    transaction: any,
    approverId: string,
    stage: ApprovalStage,
    action: ApprovalAction
  ): Promise<void> {
    // Create or update approval record (upsert to handle unique constraint)
    await prisma.transactionApproval.upsert({
      where: {
        transactionId_stage: {
          transactionId: transaction.id,
          stage,
        },
      },
      update: {
        approverId,
        action: action.action,
        comments: action.comments,
        createdAt: new Date(), // Update timestamp for new action
      },
      create: {
        transactionId: transaction.id,
        approverId,
        stage,
        action: action.action,
        comments: action.comments,
      },
    });

    let newStatus: TransactionStatus;
    let newStage: ApprovalStage | null = null;
    let notificationTitle: string;
    let notificationMessage: string;

    switch (action.action) {
      case "SUBMITTED":
        // Only supervisors can submit transactions for approval
        if (stage !== ApprovalStage.SUPERVISOR) {
          throw new OperationalError(
            "Only supervisors can submit transactions for approval. This action is restricted to users with supervisor role.",
            403 // Forbidden status code
          );
        }
        newStatus = TransactionStatus.IN_PROGRESS;
        newStage = ApprovalStage.HEAD_CONSUMER_LENDING; // First actual approver
        notificationTitle = "Transaction Submitted for Approval";
        notificationMessage = `Transaction ${transaction.transactionId} has been submitted for approval.`;

        // Notify Head CL (first approver)
        const headCLUsers = await this.getUsersForStage(
          ApprovalStage.HEAD_CONSUMER_LENDING
        );
        for (const user of headCLUsers) {
          await NotificationService.createNotification({
            userId: user.id,
            transactionId: transaction.id,
            type: "TRANSACTION_SUBMITTED",
            title: "Transaction Pending Approval",
            message: `Transaction ${transaction.transactionId} is pending your approval.`,
          });
        }

        // REMOVED: Email notifications to Head CL users
        // Transaction workflow emails have been disabled - only in-app notifications are sent
        if (headCLUsers.length > 0) {
          console.log(
            `📧 [DISABLED] Would send email notifications to ${headCLUsers.length} Head CL users for transaction ${transaction.transactionId}`
          );
          // Email sending disabled - only in-app notifications are sent above
        }
        break;

      case "APPROVED":
        // Only actual approvers can approve (Head CL, Head Risk)
        const approverStages: ApprovalStage[] = [
          ApprovalStage.HEAD_CONSUMER_LENDING,
          ApprovalStage.HEAD_RISK_MANAGEMENT,
        ];
        if (!approverStages.includes(stage)) {
          throw new OperationalError(
            "Only Head Consumer Lending or Head Risk Management can approve transactions. This action is restricted to senior approval roles.",
            403 // Forbidden status code
          );
        }

        // Check if this is the final approval stage
        const nextStage = this.getNextApprovalStage(stage);

        if (nextStage) {
          newStatus = TransactionStatus.IN_PROGRESS;
          newStage = nextStage;
          notificationTitle = "Transaction Approved";
          notificationMessage = `Transaction ${transaction.transactionId} has been approved and moved to the next stage.`;

          // Notify next stage approvers
          const nextRoleUsers = await this.getUsersForStage(nextStage);
          for (const user of nextRoleUsers) {
            await NotificationService.createNotification({
              userId: user.id,
              transactionId: transaction.id,
              type: "TRANSACTION_SUBMITTED",
              title: "Transaction Pending Approval",
              message: `Transaction ${transaction.transactionId} is pending your approval.`,
            });
          }

          // REMOVED: Email notifications to next approval stage users
          // Transaction workflow emails have been disabled - only in-app notifications are sent
          if (nextRoleUsers.length > 0) {
            const roleDisplayName = this.getRoleDisplayName(nextStage);
            console.log(
              `📧 [DISABLED] Would send email notifications to ${nextRoleUsers.length} ${roleDisplayName} users for transaction ${transaction.transactionId}`
            );
            // Email sending disabled - only in-app notifications are sent above
          }
        } else {
          newStatus = TransactionStatus.APPROVED;
          newStage = ApprovalStage.ACCOUNTANT; // Move to accountant for disbursement
          notificationTitle = "Transaction Fully Approved";
          notificationMessage = `Transaction ${transaction.transactionId} has been fully approved and is ready for disbursement.`;

          // Notify accountants
          const accountantUsers = await this.getUsersForStage(
            ApprovalStage.ACCOUNTANT
          );
          for (const user of accountantUsers) {
            await NotificationService.createNotification({
              userId: user.id,
              transactionId: transaction.id,
              type: "TRANSACTION_APPROVED",
              title: "Transaction Ready for Disbursement",
              message: `Transaction ${transaction.transactionId} is ready for disbursement.`,
            });
          }

          // REMOVED: Email notifications to accountants for disbursement
          // Transaction workflow emails have been disabled - only in-app notifications are sent
          if (accountantUsers.length > 0) {
            const roleDisplayName = this.getRoleDisplayName(
              ApprovalStage.ACCOUNTANT
            );
            console.log(
              `📧 [DISABLED] Would send email notifications to ${accountantUsers.length} ${roleDisplayName} users for disbursement of transaction ${transaction.transactionId}`
            );
            // Email sending disabled - only in-app notifications are sent above
          }
        }
        break;

      case "REJECTED":
        newStatus = TransactionStatus.REJECTED;
        notificationTitle = "Transaction Rejected";
        notificationMessage = `Transaction ${
          transaction.transactionId
        } has been rejected. ${action.comments || ""}`;
        break;

      case "SENT_BACK":
        newStatus = TransactionStatus.SENT_BACK;
        newStage = ApprovalStage.ACCOUNT_OFFICER;
        notificationTitle = "Transaction Sent Back";
        notificationMessage = `Transaction ${
          transaction.transactionId
        } has been sent back for corrections. ${action.comments || ""}`;

        // Notify all previous approvers that the transaction was sent back
        await this.notifyPreviousApprovers(
          transaction.id,
          transaction.transactionId
        );
        break;

      case "DISBURSED":
        // Only accountants can disburse
        if (stage !== ApprovalStage.ACCOUNTANT) {
          throw new OperationalError(
            "Only accountants can disburse transactions. This action is restricted to users with accountant role.",
            403 // Forbidden status code
          );
        }
        newStatus = TransactionStatus.DISBURSED;
        notificationTitle = "Transaction Disbursed";
        notificationMessage = `Transaction ${
          transaction.transactionId
        } has been disbursed. ${action.comments || ""}`;
        break;

      case "LOAN_REPAID":
        // Only accountants can mark loans as repaid
        if (stage !== ApprovalStage.ACCOUNTANT) {
          throw new OperationalError(
            "Only accountants can mark loans as repaid. This action is restricted to users with accountant role.",
            403 // Forbidden status code
          );
        }
        // Validate that the transaction is currently disbursed
        if (transaction.status !== TransactionStatus.DISBURSED) {
          throw new OperationalError(
            "Only disbursed loans can be marked as repaid. The loan must be in disbursed status to be marked as repaid.",
            400 // Bad request status code
          );
        }
        newStatus = TransactionStatus.LOAN_REPAID;
        notificationTitle = "Loan Repaid";
        notificationMessage = `Loan ${
          transaction.transactionId
        } has been marked as repaid. ${action.comments || ""}`;
        break;
    }

    // Update transaction
    const updateData: any = {
      status: newStatus,
      updatedAt: new Date(),
    };

    if (newStage !== null) {
      updateData.currentStage = newStage;
    }

    if (action.action === "REJECTED") {
      updateData.rejectedAt = new Date();
      updateData.rejectionReason = action.comments;
    }

    if (action.action === "SENT_BACK") {
      updateData.sentBackReason = action.comments;
    }

    if (action.action === "DISBURSED") {
      updateData.disbursedAt = new Date();
      // Note: completedAt will be set when the loan tenor expires or is paid off
    }

    if (action.action === "LOAN_REPAID") {
      const now = new Date();
      updateData.repaidAt = now;
      updateData.completedAt = now; // Mark as completed when repaid
    }

    await prisma.transaction.update({
      where: { id: transaction.id },
      data: updateData,
    });

    // Send notification to transaction creator
    await NotificationService.createNotification({
      userId: transaction.createdById,
      transactionId: transaction.id,
      type: `TRANSACTION_${action.action}` as any,
      title: notificationTitle,
      message: notificationMessage,
    });

    // REMOVED: Email notification to transaction creator
    // Transaction workflow emails have been disabled - only in-app notifications are sent
    console.log(
      `📧 [DISABLED] Would send email notification to transaction creator: ${transaction.createdBy.email}`
    );
    console.log(
      `📝 [DISABLED] Subject: Transaction ${action.action} - ${transaction.transactionId}`
    );
    console.log(`📝 [DISABLED] Message: ${notificationMessage}`);
    // Email sending disabled - only in-app notifications are sent above
  }

  private static getNextApprovalStage(
    currentStage: ApprovalStage
  ): ApprovalStage | null {
    switch (currentStage) {
      case ApprovalStage.HEAD_CONSUMER_LENDING:
        return ApprovalStage.HEAD_RISK_MANAGEMENT;

      case ApprovalStage.HEAD_RISK_MANAGEMENT:
        // After Head Risk Management approval, go directly to accountant for all amounts
        return null; // This will trigger final approval (skip MANAGING_DIRECTOR)

      default:
        return null;
    }
  }

  private static async getUsersForStage(
    stage: ApprovalStage
  ): Promise<
    { id: string; email: string; firstName: string; lastName: string }[]
  > {
    const roleMap: Record<ApprovalStage, UserRole> = {
      [ApprovalStage.ACCOUNT_OFFICER]: UserRole.ACCOUNT_OFFICER,
      [ApprovalStage.SUPERVISOR]: UserRole.SUPERVISOR,
      [ApprovalStage.HEAD_CONSUMER_LENDING]: UserRole.HEAD_CONSUMER_LENDING,
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: UserRole.HEAD_RISK_MANAGEMENT,
      [ApprovalStage.MANAGING_DIRECTOR]: UserRole.MANAGING_DIRECTOR, // Keep for data integrity
      [ApprovalStage.ACCOUNTANT]: UserRole.ACCOUNTANT,
    };

    return await prisma.user.findMany({
      where: {
        role: roleMap[stage],
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      },
    });
  }

  private static getRoleDisplayName(stage: ApprovalStage): string {
    const roleDisplayNames: Record<ApprovalStage, string> = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer",
      [ApprovalStage.SUPERVISOR]: "Supervisor",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head of Consumer Lending",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head of Risk Management",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director (Legacy)", // Keep for existing data
      [ApprovalStage.ACCOUNTANT]: "Accountant",
    };

    return roleDisplayNames[stage] || stage;
  }

  private static async notifyPreviousApprovers(
    transactionId: string,
    transactionIdString: string
  ): Promise<void> {
    // Get all users who have approved this transaction
    const approvals = await prisma.transactionApproval.findMany({
      where: { transactionId },
      include: {
        approver: true,
      },
    });

    // Notify each approver
    for (const approval of approvals) {
      await NotificationService.createNotification({
        userId: approval.approverId,
        transactionId,
        type: "TRANSACTION_SENT_BACK",
        title: "Transaction Sent Back",
        message: `Transaction ${transactionIdString} that you approved has been sent back for corrections.`,
      });
    }
  }

  static async getTransactionsForApproval(
    userRole: UserRole,
    page: number = 1,
    limit: number = 10
  ): Promise<{ transactions: any[]; total: number }> {
    const stage = this.ROLE_TO_STAGE[userRole];

    if (!stage) {
      return { transactions: [], total: 0 };
    }

    const skip = (page - 1) * limit;

    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // For supervisors, only show SUBMITTED transactions (ready for review)
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }

    // For accountants, also include APPROVED transactions ready for disbursement
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where: whereClause,
        include: {
          createdBy: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          documents: {
            select: {
              id: true,
              fileName: true,
              originalName: true,
              filePath: true,
              fileSize: true,
              fileType: true,
              mimeType: true,
              uploadedAt: true,
            },
            orderBy: {
              uploadedAt: "desc",
            },
          },
        },
        orderBy: {
          submittedAt: "asc",
        },
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where: whereClause }),
    ]);

    // Format the transactions to include enhanced document information
    const formattedTransactions = transactions.map((transaction) => ({
      ...transaction,
      documents: transaction.documents.map((doc) => ({
        id: doc.id,
        fileName: doc.fileName,
        originalName: doc.originalName,
        fileSize: doc.fileSize,
        fileType: doc.fileType,
        mimeType: doc.mimeType,
        uploadedAt: doc.uploadedAt,
        fileUrl: FileUploadUtils.getFileUrl(doc.filePath),
        formattedSize: FileUploadUtils.formatFileSize(doc.fileSize),
      })),
    }));

    return { transactions: formattedTransactions, total };
  }

  static async getApprovalHistory(transactionId: string): Promise<any[]> {
    return await prisma.transactionApproval.findMany({
      where: { transactionId },
      include: {
        approver: {
          select: {
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });
  }

  static async getDetailedApprovalHistory(transactionId: string): Promise<{
    transactionId: string;
    approvalHistory: Array<{
      stage: string;
      stageName: string;
      action: string;
      approver: {
        id: string;
        name: string;
        role: string;
      };
      comments: string | null;
      timestamp: string;
      formattedDate: string;
    }>;
  }> {
    // First, verify the transaction exists and get its transactionId
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: { transactionId: true },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found. The requested transaction does not exist or may have been deleted.",
        404 // Not found status code
      );
    }

    // Get approval history with approver details
    const approvals = await prisma.transactionApproval.findMany({
      where: { transactionId },
      include: {
        approver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Format the approval history
    const approvalHistory = approvals.map((approval) => {
      const stageName = this.getStageDisplayName(approval.stage);
      const formattedDate = new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
      }).format(new Date(approval.createdAt));

      return {
        stage: approval.stage,
        stageName,
        action: approval.action,
        approver: {
          id: approval.approver.id,
          name: `${approval.approver.firstName} ${approval.approver.lastName}`,
          role: approval.approver.role,
        },
        comments: approval.comments,
        timestamp: approval.createdAt.toISOString(),
        formattedDate,
      };
    });

    return {
      transactionId: transaction.transactionId,
      approvalHistory,
    };
  }

  private static getStageDisplayName(stage: ApprovalStage): string {
    const stageNames = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer",
      [ApprovalStage.SUPERVISOR]: "Supervisor Review",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head Consumer Lending",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head Risk Management",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director (Legacy)", // Keep for existing data
      [ApprovalStage.ACCOUNTANT]: "Accountant (Disbursement)",
    };

    return stageNames[stage] || stage;
  }

  static async getPendingApprovals(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const stage = this.ROLE_TO_STAGE[userRole];

    if (!stage) {
      return {
        transactions: [],
        total: 0,
        page: filters.page || 1,
        limit: filters.limit || 10,
        totalPages: 0,
      };
    }

    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const skip = (page - 1) * limit;

    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // For supervisors, only show SUBMITTED transactions
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }

    // For accountants, also include APPROVED transactions
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    // Apply filters
    if (filters.accountOfficerId) {
      whereClause.createdById = filters.accountOfficerId;
    }

    // Handle multiple account officer IDs (takes precedence over single accountOfficerId)
    if (filters.accountOfficerIds && filters.accountOfficerIds.length > 0) {
      whereClause.createdById = {
        in: filters.accountOfficerIds,
      };
    }

    if (filters.loanType) {
      whereClause.loanType = filters.loanType;
    }

    // Apply date range filters
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};

      if (filters.dateFrom) {
        whereClause.createdAt.gte = new Date(filters.dateFrom);
      }

      if (filters.dateTo) {
        // Add 23:59:59 to include the entire day
        const endDate = new Date(filters.dateTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt.lte = endDate;
      }
    }

    // Apply search filter (search by transaction ID, name, or email)
    if (filters.search) {
      whereClause.OR = [
        { transactionId: { contains: filters.search, mode: "insensitive" } },
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    // Add logging for debugging
    console.log(
      `🔍 Getting pending approvals for ${userRole} (${userId}) at stage ${stage}:`,
      {
        whereClause: JSON.stringify(whereClause, null, 2),
        filters,
      }
    );

    // Get all transactions at this stage with the required status
    const allTransactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: {
          select: {
            id: true,
            originalName: true,
            fileType: true,
          },
        },
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Filter transactions: exclude those where user has already acted (unless resubmitted after send-back)
    const eligibleTransactions = allTransactions.filter((transaction) => {
      // Check if user has any approvals at this stage
      const userApprovalsAtStage = transaction.approvals.filter(
        (approval) => approval.approverId === userId && approval.stage === stage
      );

      if (userApprovalsAtStage.length === 0) {
        // User has never acted on this transaction at this stage - include it
        return true;
      }

      // User has acted at this stage - check if transaction was resubmitted after send-back
      // Get the user's most recent approval at this stage
      const lastUserApproval = userApprovalsAtStage.reduce((latest, current) =>
        new Date(current.createdAt) > new Date(latest.createdAt)
          ? current
          : latest
      );

      // Check if the transaction was resubmitted after the user's last action
      // This happens when submittedAt is more recent than the user's last approval
      const wasResubmittedAfterUserAction =
        transaction.submittedAt &&
        new Date(transaction.submittedAt) >
          new Date(lastUserApproval.createdAt);

      console.log(`🔍 Transaction ${transaction.transactionId} filtering:`, {
        userId,
        stage,
        transactionStatus: transaction.status,
        transactionCurrentStage: transaction.currentStage,
        submittedAt: transaction.submittedAt,
        userActionsCount: userApprovalsAtStage.length,
        lastUserApprovalDate: lastUserApproval.createdAt,
        lastUserApprovalAction: lastUserApproval.action,
        wasResubmittedAfterUserAction,
        willInclude: wasResubmittedAfterUserAction,
      });

      // Include if transaction was resubmitted after user's last action
      return wasResubmittedAfterUserAction;
    });

    // Apply pagination to filtered results
    const paginatedTransactions = eligibleTransactions.slice(
      skip,
      skip + limit
    );

    const formattedTransactions = paginatedTransactions.map((transaction) => ({
      id: transaction.id,
      customerName: `${transaction.firstName} ${transaction.lastName}`,
      loanType: transaction.loanType,
      requestedAmount: transaction.requestedAmount,
      status: transaction.status,
      currentStage: transaction.currentStage,
      createdAt: transaction.createdAt,
      submittedAt: transaction.submittedAt,
      createdBy: transaction.createdBy,
      documentsCount: transaction.documents.length,
      approvals: transaction.approvals,
      canApprove: true,
      stageInfo: this.getStageInfo(stage, transaction.status),
    }));

    const total = eligibleTransactions.length;
    const totalPages = Math.ceil(total / limit);

    console.log(`📊 Pending approvals result for ${userRole} (${userId}):`, {
      totalTransactionsAtStage: allTransactions.length,
      eligibleAfterFiltering: total,
      page,
      limit,
      totalPages,
      returnedCount: formattedTransactions.length,
    });

    return {
      transactions: formattedTransactions,
      total,
      page,
      limit,
      totalPages,
    };
  }

  private static getStageInfo(
    stage: ApprovalStage,
    status: TransactionStatus
  ): any {
    const stageNames = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer",
      [ApprovalStage.SUPERVISOR]: "Supervisor Review",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head Consumer Lending",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head Risk Management",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director (Legacy)", // Keep for existing data
      [ApprovalStage.ACCOUNTANT]: "Accountant (Disbursement)",
    };

    const actionLabels = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Create/Edit",
      [ApprovalStage.SUPERVISOR]:
        status === TransactionStatus.SUBMITTED ? "Review & Submit" : "Process",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Approve/Reject/Send Back",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Approve/Reject/Send Back",
      [ApprovalStage.MANAGING_DIRECTOR]: "Legacy Stage (No Actions)", // No actions available
      [ApprovalStage.ACCOUNTANT]: "Disburse/Send Back",
    };

    return {
      stageName: stageNames[stage] || stage,
      actionLabel: actionLabels[stage] || "Process",
      isUrgent: status === TransactionStatus.SUBMITTED,
    };
  }

  /**
   * Get a specific transaction for the "My Requests" page
   * Users can see transactions that are at their approval stage or that they've previously acted on
   */
  static async getMyRequestTransaction(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<any> {
    const userStage = this.ROLE_TO_STAGE[userRole];

    if (!userStage) {
      throw new OperationalError(
        "Invalid user role for approval requests. Your role does not have permission to access approval requests.",
        403 // Forbidden status code
      );
    }

    // Build where clause for transactions the user can access
    const whereClause: any = { id: transactionId };

    // Super admin can see all transactions
    if (userRole === UserRole.SUPER_ADMIN) {
      // No additional restrictions
    } else {
      // Other roles can see:
      // 1. Transactions currently at their stage
      // 2. Transactions they have previously acted on
      whereClause.OR = [
        // Transactions at their current stage
        {
          currentStage: userStage,
          status: {
            in: [
              TransactionStatus.SUBMITTED,
              TransactionStatus.IN_PROGRESS,
              ...(userRole === UserRole.ACCOUNTANT
                ? [TransactionStatus.APPROVED]
                : []),
            ],
          },
        },
        // Transactions they have previously acted on
        {
          approvals: {
            some: {
              approverId: userId,
              stage: userStage,
            },
          },
        },
      ];
    }

    const transaction = await prisma.transaction.findFirst({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: true,
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or access denied. You may not have permission to view this transaction or it may not exist.",
        404 // Not found status code
      );
    }

    // Add user-specific context
    const userApproval = transaction.approvals.find(
      (approval: any) =>
        approval.approverId === userId && approval.stage === userStage
    );

    const validStatusesForAction = [
      TransactionStatus.SUBMITTED,
      TransactionStatus.IN_PROGRESS,
      TransactionStatus.APPROVED,
    ];

    const canTakeAction =
      transaction.currentStage === userStage &&
      !userApproval &&
      validStatusesForAction.includes(transaction.status as any);

    return {
      ...transaction,
      userContext: {
        canTakeAction,
        hasActed: !!userApproval,
        userAction: userApproval?.action || null,
        userComments: userApproval?.comments || null,
        actionDate: userApproval?.createdAt || null,
      },
    };
  }

  /**
   * REMOVED: Old CSV export method - now using XLSX only
   */
  private static async getRawPendingApprovalsForCSV_DEPRECATED(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
    } = {}
  ) {
    // Get the approval stage for this user role
    const stage = this.ROLE_TO_STAGE[userRole];
    if (!stage) {
      console.log(`❌ No approval stage found for role: ${userRole}`);
      return [];
    }

    // Build where clause for filtering transactions
    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.APPROVED],
      },
    };

    // Apply filters
    if (filters.accountOfficerId) {
      whereClause.createdById = filters.accountOfficerId;
    } else if (
      filters.accountOfficerIds &&
      filters.accountOfficerIds.length > 0
    ) {
      whereClause.createdById = {
        in: filters.accountOfficerIds,
      };
    }

    if (filters.loanType) {
      whereClause.loanType = filters.loanType;
    }

    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};
      if (filters.dateFrom) {
        whereClause.createdAt.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        const endDate = new Date(filters.dateTo);
        endDate.setDate(endDate.getDate() + 1);
        whereClause.createdAt.lt = endDate;
      }
    }

    // Apply search filter (search by transaction ID, name, or email)
    if (filters.search) {
      whereClause.OR = [
        { transactionId: { contains: filters.search, mode: "insensitive" } },
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    console.log(
      `🔍 CSV Export - Querying transactions for ${userRole} at stage ${stage}:`,
      {
        whereClause,
        userId,
      }
    );

    // Get all transactions at this stage with full data
    const allTransactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },

        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Filter transactions: exclude those where user has already acted (unless resubmitted after send-back)
    const eligibleTransactions = allTransactions.filter((transaction) => {
      // Check if user has any approvals at this stage
      const userApprovalsAtStage = transaction.approvals.filter(
        (approval) => approval.approverId === userId && approval.stage === stage
      );

      if (userApprovalsAtStage.length === 0) {
        // User has never acted on this transaction at this stage - include it
        return true;
      }

      // User has acted at this stage - check if transaction was resubmitted after send-back
      const lastUserApproval = userApprovalsAtStage.reduce((latest, current) =>
        new Date(current.createdAt) > new Date(latest.createdAt)
          ? current
          : latest
      );

      // Check if the transaction was resubmitted after the user's last action
      const wasResubmittedAfterUserAction =
        transaction.submittedAt &&
        new Date(transaction.submittedAt) >
          new Date(lastUserApproval.createdAt);

      return wasResubmittedAfterUserAction;
    });

    console.log(
      `📊 CSV Export - Found ${eligibleTransactions.length} eligible transactions out of ${allTransactions.length} total`
    );

    return eligibleTransactions;
  }

  /**
   * Get comprehensive transaction data for export with all required fields
   */
  private static async getComprehensiveTransactionDataForExport(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
    } = {}
  ) {
    console.log("🔍 getComprehensiveTransactionDataForExport called with:", {
      userRole,
      userId,
      filters,
    });

    // Get the approval stage for this user role
    const stage = this.ROLE_TO_STAGE[userRole];
    if (!stage) {
      console.log(`❌ No approval stage found for role: ${userRole}`);
      return [];
    }

    console.log(`✅ Found stage for role ${userRole}: ${stage}`);

    // Build where clause for filtering transactions
    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // For supervisors, only show SUBMITTED transactions
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }

    // For accountants, also include APPROVED transactions
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    // Apply filters
    if (filters.accountOfficerId) {
      whereClause.createdById = filters.accountOfficerId;
    }

    // Handle multiple account officer IDs (takes precedence over single accountOfficerId)
    if (filters.accountOfficerIds && filters.accountOfficerIds.length > 0) {
      whereClause.createdById = {
        in: filters.accountOfficerIds,
      };
    }

    if (filters.loanType) {
      whereClause.loanType = filters.loanType;
    }

    // Apply date range filters
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};
      if (filters.dateFrom) {
        whereClause.createdAt.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        const endDate = new Date(filters.dateTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt.lte = endDate;
      }
    }

    // Apply search filter (search by transaction ID, name, or email)
    if (filters.search) {
      whereClause.OR = [
        { transactionId: { contains: filters.search, mode: "insensitive" } },
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    console.log(
      `🔍 Export - Querying comprehensive transaction data for ${userRole} at stage ${stage}:`,
      {
        whereClause: JSON.stringify(whereClause, null, 2),
        userId,
      }
    );

    // Get all transactions with comprehensive data including all required fields
    const allTransactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },

        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Filter transactions: exclude those where user has already acted (unless resubmitted after send-back)
    const eligibleTransactions = allTransactions.filter((transaction) => {
      // Check if user has any approvals at this stage
      const userApprovalsAtStage = transaction.approvals.filter(
        (approval) => approval.approverId === userId && approval.stage === stage
      );

      if (userApprovalsAtStage.length === 0) {
        // User hasn't acted on this transaction at this stage
        return true;
      }

      // Check if transaction was resubmitted after user's last action
      const lastUserApproval = userApprovalsAtStage[0]; // Most recent approval by this user
      const wasResubmittedAfterApproval =
        transaction.submittedAt &&
        new Date(transaction.submittedAt) >
          new Date(lastUserApproval.createdAt);

      return wasResubmittedAfterApproval;
    });

    console.log(
      `📊 Export - Found ${eligibleTransactions.length} eligible transactions out of ${allTransactions.length} total`
    );

    return eligibleTransactions;
  }

  /**
   * Export pending approvals as XLSX - DEFINITIVE VERSION
   */
  static async exportPendingApprovalsXLSX(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
    } = {}
  ): Promise<Buffer> {
    console.log("🚀 DEFINITIVE XLSX EXPORT - NO MORE CSV!", {
      userRole,
      userId,
      filters,
      timestamp: new Date().toISOString(),
    });

    try {
      console.log(
        "🔍 Using comprehensive database query for XLSX export with all required fields..."
      );

      // Get comprehensive transaction data with all required fields for XLSX export
      const transactions =
        await this.getComprehensiveTransactionDataForXLSXExport(
          userRole,
          userId,
          filters
        );

      console.log(
        `📊 Exporting ${transactions.length} pending approvals to XLSX`
      );

      if (!Array.isArray(transactions)) {
        console.error("❌ Transactions is not an array:", typeof transactions);
        throw new Error("Invalid transaction data returned");
      }

      if (transactions.length === 0) {
        console.log("⚠️ No transactions found for XLSX export");
        // Return empty XLSX file
        const emptyWorkbook = XLSX.utils.book_new();
        const emptyWorksheet = XLSX.utils.aoa_to_sheet([
          [
            "Transaction ID",
            "Customer Name",
            "Account name",
            "Account number",
            "Bank name",
            "Requested amount",
            "IPPIS No.",
            "Phone number",
            "BVN",
            "Organisation name",
            "Loan tenure",
            "Account Officer",
            "Gender",
            "DOB",
            "Marital Status",
            "NIN",
            "Email address",
            "House address",
            "Employment date",
            "Next of kin name",
            "Next of kin phone No",
            "Next of kin email",
            "Next of kin Address",
          ],
        ]);
        XLSX.utils.book_append_sheet(
          emptyWorkbook,
          emptyWorksheet,
          "Pending Approvals"
        );
        return Buffer.from(
          XLSX.write(emptyWorkbook, { type: "buffer", bookType: "xlsx" })
        );
      }

      // Prepare data for XLSX export with exact required columns
      console.log(
        "🔍 Mapping transaction data to XLSX format with all 24 columns (including Date to Accountant)..."
      );

      const xlsxData = transactions.map((transaction: any) => {
        console.log(`📋 Processing transaction: ${transaction.transactionId}`);

        // Find when transaction reached accountant stage (approved by Managing Director)
        const accountantStageDate = this.getAccountantStageDate(
          transaction.approvals
        );

        return {
          "Transaction ID": transaction.transactionId || "",
          "Customer Name": `${transaction.firstName || ""} ${
            transaction.middleName || ""
          } ${transaction.lastName || ""}`.trim(),
          "Account name": transaction.accountName || "",
          "Account number": transaction.accountNumber || "",
          "Bank name": transaction.bankName || "",
          "Requested amount": transaction.requestedAmount || 0,
          "IPPIS No.": transaction.ippisNumber || "",
          "Phone number": transaction.phoneNumber || "",
          BVN: transaction.bvn || "",
          "Organisation name": transaction.organizationName || "",
          "Loan tenure": transaction.loanTenor || "",
          "Account Officer": transaction.createdBy
            ? `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`
            : "",
          Gender: transaction.gender || "",
          DOB: transaction.dateOfBirth
            ? new Date(transaction.dateOfBirth).toLocaleDateString("en-US")
            : "",
          "Marital Status": transaction.maritalStatus || "",
          NIN: transaction.nin || "",
          "Email address": transaction.email || "",
          "House address": [
            transaction.street,
            transaction.city,
            transaction.state,
            transaction.postalCode,
          ]
            .filter(Boolean)
            .join(", "),
          "Employment date": transaction.employmentDate
            ? new Date(transaction.employmentDate).toLocaleDateString("en-US")
            : "",
          "Date to Accountant": accountantStageDate,
          "Next of kin name": `${transaction.nokFirstName || ""} ${
            transaction.nokMiddleName || ""
          } ${transaction.nokLastName || ""}`.trim(),
          "Next of kin phone No": transaction.nokPhoneNumber || "",
          "Next of kin email": transaction.nokEmail || "",
          "Next of kin Address": [
            transaction.nokStreet,
            transaction.nokCity,
            transaction.nokState,
            transaction.nokPostalCode,
          ]
            .filter(Boolean)
            .join(", "),
        };
      });

      console.log(
        `✅ XLSX data prepared for ${xlsxData.length} transactions with all 24 columns (including Date to Accountant)`
      );

      // Create XLSX workbook
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(xlsxData);

      // Set column widths for better readability
      const columnWidths = [
        { wch: 15 }, // Transaction ID
        { wch: 25 }, // Customer Name
        { wch: 25 }, // Account name
        { wch: 15 }, // Account number
        { wch: 20 }, // Bank name
        { wch: 15 }, // Requested amount
        { wch: 15 }, // IPPIS No.
        { wch: 15 }, // Phone number
        { wch: 15 }, // BVN
        { wch: 30 }, // Organisation name
        { wch: 12 }, // Loan tenure
        { wch: 25 }, // Account Officer
        { wch: 10 }, // Gender
        { wch: 12 }, // DOB
        { wch: 15 }, // Marital Status
        { wch: 15 }, // NIN
        { wch: 25 }, // Email address
        { wch: 40 }, // House address
        { wch: 15 }, // Employment date
        { wch: 25 }, // Next of kin name
        { wch: 15 }, // Next of kin phone No
        { wch: 25 }, // Next of kin email
        { wch: 40 }, // Next of kin Address
      ];
      worksheet["!cols"] = columnWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, "Pending Approvals");

      // Generate XLSX buffer
      const xlsxBuffer = XLSX.write(workbook, {
        type: "buffer",
        bookType: "xlsx",
      });

      console.log(`📄 XLSX file generated successfully:`, {
        size: xlsxBuffer.length,
        transactions: xlsxData.length,
      });

      return Buffer.from(xlsxBuffer);
    } catch (error) {
      console.error("❌ XLSX export error:", error);
      throw new OperationalError(
        `Failed to generate XLSX export: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        500
      );
    }
  }

  /**
   * Get comprehensive transaction data for XLSX export with ALL required fields
   */
  private static async getComprehensiveTransactionDataForXLSXExport(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
    } = {}
  ) {
    console.log("🔍 Building comprehensive database query for XLSX export...");

    // Get the approval stage for this user role
    const stage = this.ROLE_TO_STAGE[userRole];
    if (!stage) {
      console.log(`❌ No approval stage found for role: ${userRole}`);
      return [];
    }

    console.log(`✅ Found stage for role ${userRole}: ${stage}`);

    // Build where clause for filtering transactions
    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // For supervisors, only show SUBMITTED transactions
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }

    // For accountants, also include APPROVED transactions
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    // Apply filters
    if (filters.accountOfficerId) {
      whereClause.createdById = filters.accountOfficerId;
    }

    // Handle multiple account officer IDs
    if (filters.accountOfficerIds && filters.accountOfficerIds.length > 0) {
      whereClause.createdById = {
        in: filters.accountOfficerIds,
      };
    }

    if (filters.loanType) {
      whereClause.loanType = filters.loanType;
    }

    // Apply date range filters
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};

      if (filters.dateFrom) {
        whereClause.createdAt.gte = new Date(filters.dateFrom);
      }

      if (filters.dateTo) {
        const endDate = new Date(filters.dateTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt.lte = endDate;
      }
    }

    // Apply search filter
    if (filters.search) {
      whereClause.OR = [
        { transactionId: { contains: filters.search, mode: "insensitive" } },
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    console.log("🔍 Executing comprehensive database query with all fields...");

    // Get ALL transaction data with comprehensive field selection
    const allTransactions = await prisma.transaction.findMany({
      where: whereClause,
      select: {
        // Basic transaction info
        id: true,
        transactionId: true,
        loanType: true,
        requestedAmount: true,
        loanTenor: true,
        status: true,
        currentStage: true,
        createdAt: true,
        updatedAt: true,

        // Personal Information (ALL fields)
        firstName: true,
        middleName: true,
        lastName: true,
        email: true,
        phoneNumber: true,
        dateOfBirth: true,
        gender: true,
        maritalStatus: true,
        bvn: true,
        nin: true,

        // Address Information
        street: true,
        city: true,
        state: true,
        postalCode: true,

        // Employment Information
        organizationName: true,
        employmentDate: true,
        ippisNumber: true,

        // Account Information
        accountName: true,
        accountNumber: true,
        bankName: true,

        // Next of Kin Information (ALL fields)
        nokFirstName: true,
        nokMiddleName: true,
        nokLastName: true,
        nokRelationship: true,
        nokPhoneNumber: true,
        nokEmail: true,
        nokStreet: true,
        nokCity: true,
        nokState: true,
        nokPostalCode: true,

        // Account Officer (Created By)
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            role: true,
          },
        },

        // Approvals for filtering and date tracking
        approvals: {
          select: {
            id: true,
            stage: true,
            action: true,
            approverId: true,
            createdAt: true,
            comments: true,
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(
      `📊 Found ${allTransactions.length} total transactions from database`
    );

    // Filter out transactions the user has already acted on (unless resubmitted)
    const eligibleTransactions = allTransactions.filter((transaction: any) => {
      const userApprovals = transaction.approvals.filter(
        (approval: any) =>
          approval.approverId === userId && approval.stage === stage
      );

      if (userApprovals.length === 0) {
        return true; // User hasn't acted on this transaction
      }

      // Check if transaction was resubmitted after user's last approval
      const lastUserApproval = userApprovals.sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      const wasResubmittedAfterApproval =
        transaction.updatedAt > lastUserApproval.createdAt;
      return wasResubmittedAfterApproval;
    });

    console.log(
      `📊 Export - Found ${eligibleTransactions.length} eligible transactions out of ${allTransactions.length} total`
    );

    return eligibleTransactions;
  }

  /**
   * Get the date when transaction reached accountant stage
   * This is when the Managing Director approved it and it moved to accountant
   */
  private static getAccountantStageDate(approvals: any[]): string {
    if (!approvals || approvals.length === 0) {
      return "";
    }

    // Find the Managing Director approval that moved the transaction to accountant stage
    const mdApproval = approvals.find(
      (approval: any) =>
        approval.stage === "MANAGING_DIRECTOR" && approval.action === "APPROVED"
    );

    if (mdApproval && mdApproval.createdAt) {
      return new Date(mdApproval.createdAt).toLocaleDateString("en-US");
    }

    // If no MD approval found, check if transaction is currently at accountant stage
    // and use the most recent approval date as fallback
    const latestApproval = approvals
      .filter((approval: any) => approval.action === "APPROVED")
      .sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

    if (latestApproval && latestApproval.createdAt) {
      return new Date(latestApproval.createdAt).toLocaleDateString("en-US");
    }

    return "";
  }

  /**
   * Generate filename for pending approvals XLSX export
   */
  static generatePendingApprovalsXLSXFilename(
    userRole: UserRole,
    filters: any = {}
  ): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");
    const timestamp = `${year}${month}${day}-${hours}${minutes}${seconds}`;

    // Create descriptive filename based on filters
    let filename = `pending-approvals-${userRole.toLowerCase()}`;

    if (filters.loanType) {
      filename += `-${filters.loanType.toLowerCase()}`;
    }

    if (filters.search) {
      const searchTerm = filters.search
        .replace(/[^a-zA-Z0-9]/g, "")
        .substring(0, 10);
      if (searchTerm) {
        filename += `-search-${searchTerm}`;
      }
    }

    if (filters.dateFrom && filters.dateTo) {
      const fromDate = new Date(filters.dateFrom).toISOString().split("T")[0];
      const toDate = new Date(filters.dateTo).toISOString().split("T")[0];
      filename += `-${fromDate}-to-${toDate}`;
    } else if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom).toISOString().split("T")[0];
      filename += `-from-${fromDate}`;
    } else if (filters.dateTo) {
      const toDate = new Date(filters.dateTo).toISOString().split("T")[0];
      filename += `-until-${toDate}`;
    }

    filename += `-${timestamp}.xlsx`;

    console.log("🎯 XLSX filename generated:", filename);

    // FORCE XLSX EXTENSION - NO CSV ALLOWED!
    if (!filename.endsWith(".xlsx")) {
      filename = filename.replace(/\.(csv|txt)$/, "") + ".xlsx";
    }

    return filename;
  }

  /**
   * TEST METHOD: Create a simple XLSX file to verify functionality
   */
  static async createTestXLSX(): Promise<Buffer> {
    console.log("🧪 Creating test XLSX file...");

    try {
      const testData = [
        {
          "Transaction ID": "TEST-001",
          "Customer Name": "Test User",
          Email: "<EMAIL>",
          Status: "Testing XLSX Export",
        },
      ];

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(testData);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Test");

      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

      console.log("✅ Test XLSX created successfully:", {
        size: buffer.length,
        isBuffer: Buffer.isBuffer(buffer),
      });

      return Buffer.from(buffer);
    } catch (error) {
      console.error("❌ Test XLSX creation failed:", error);
      throw error;
    }
  }
}
